import { useState } from 'react'
import Header from './components/share/Header'
import Footer from './components/share/Footer'
import { Route, Routes } from 'react-router-dom'
import NotFoundPage from './components/share/NotFoundPage'
import HomePage from './components/home/<USER>'
import LoginPage from './components/Auth/LoginPage'
import RegistrationPage from './components/Auth/RegistrationPage'

function App() {

  return (
    <>
      <Header />
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/register" element={<RegistrationPage />} />
        <Route path="*" element={<NotFoundPage />} />
      </Routes>
      <Footer />
    </>
  )
}

export default App
